import random
import pandas as pd

def generate_decimal_numbers():
    """生成两个小数，精确到1位或2位小数"""
    a = round(random.uniform(0.1, 99.9), random.choice([1, 2]))
    b = round(random.uniform(0.1, 99.9), random.choice([1, 2]))
    return a, b

def generate_operator():
    """生成运算符"""
    return random.choice(['+', '-'])

def ensure_positive_result(a, b, op):
    """确保减法结果不为负数"""
    if op == '-' and a < b:
        return b, a
    return a, b

def calculate_result(a, b, op):
    """计算结果并四舍五入"""
    if op == '+':
        result = a + b
    else:  # op == '-'
        result = a - b
    return round(result, 2)

def generate_problem_data():
    """生成单个题目的数据，返回独立的单元格数据"""
    a, b = generate_decimal_numbers()
    op = generate_operator()
    a, b = ensure_positive_result(a, b, op)
    result = calculate_result(a, b, op)

    return {
        '第一个数': a,
        '运算符': op,
        '第二个数': b,
        '等号': '=',
        '答案': result,
        '完整题目': f"{a} {op} {b} = {result}"
    }

# 生成100道题目
problems_data = []
for i in range(100):
    problem = generate_problem_data()
    problem['题号'] = i + 1
    problems_data.append(problem)

# 创建DataFrame，将每个组件作为独立列
df = pd.DataFrame(problems_data)

# 重新排列列的顺序
column_order = ['题号', '第一个数', '运算符', '第二个数', '等号', '答案', '完整题目']
df = df[column_order]

# 保存到Excel文件
df.to_excel('小学四年级小数加减100题_优化版.xlsx', index=False)

print("已生成100道小数加减法题目！")
print("文件保存为：小学四年级小数加减100题_优化版.xlsx")
print("\n前5道题目预览：")
print(df.head())
