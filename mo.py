import random
import pandas as pd

def generate_decimal_numbers():
    """生成两个小数，精确到1位小数"""
    a = round(random.uniform(0.1, 9.9), 1)
    b = round(random.uniform(0.1, 9.9), 1)
    return a, b

def generate_operator():
    """生成运算符"""
    return random.choice(['+', '-'])

def ensure_positive_result(a, b, op):
    """确保减法结果不为负数"""
    if op == '-' and a < b:
        return b, a
    return a, b

def calculate_result(a, b, op):
    """计算结果并四舍五入"""
    if op == '+':
        result = a + b
    else:  # op == '-'
        result = a - b
    return round(result, 2)

def generate_problem_data():
    """生成单个题目的数据，返回独立的单元格数据"""
    a, b = generate_decimal_numbers()
    op = generate_operator()
    a, b = ensure_positive_result(a, b, op)

    return {
        '第一个数': a,
        '运算符': op,
        '第二个数': b,
        '等号': '='
    }

# 生成100道题目
problems_data = []
for i in range(1000):
    problem = generate_problem_data()
    problems_data.append(problem)

# 将100题分成5组，每组20题，每组作为独立的列组
final_data = {}

for group in range(50):  # 5组，每组20题
    start_idx = group * 20
    end_idx = start_idx + 20
    group_problems = problems_data[start_idx:end_idx]

    # 为每组创建列名
    col_prefix = f"第{group+1}组_"

    # 提取每组的数据
    first_nums = [p['第一个数'] for p in group_problems]
    operators = [p['运算符'] for p in group_problems]
    second_nums = [p['第二个数'] for p in group_problems]
    equals = [p['等号'] for p in group_problems]

    # 添加到最终数据字典
    final_data[f'{col_prefix}第一个数'] = first_nums
    final_data[f'{col_prefix}运算符'] = operators
    final_data[f'{col_prefix}第二个数'] = second_nums
    final_data[f'{col_prefix}等号'] = equals

    # 在每组后添加空白列（除了最后一组）
    if group < 4:  # 不在最后一组后添加空白列
        final_data[f'空白列_{group+1}'] = ['' for _ in range(50)]

# 创建DataFrame
df = pd.DataFrame(final_data)

# 保存到Excel文件
df.to_excel('小学四年级小数加减100题_分列版.xlsx', index=False)

print("已生成100道小数加减法题目！")
print("文件保存为：小学四年级小数加减1000题_分列版.xlsx")
print("题目按每20题一组分为50列组合")
print(f"DataFrame形状: {df.shape}")
print("\n前5行预览：")
print(df.head())
