import random
import pandas as pd

def generate_decimal_problem():
    a = round(random.uniform(0.1, 99.9), random.choice([1, 2]))
    b = round(random.uniform(0.1, 99.9), random.choice([1, 2]))
    op = random.choice(['+', '-'])
    # 保证减法结果不为负
    if op == '-' and a < b:
        a, b = b, a
    return f"{a} {op} {b} = "

problems = [generate_decimal_problem() for _ in range(100)]
df = pd.DataFrame({'题目': problems})
df.to_excel('小学四年级小数加减100题.xlsx', index=False)
